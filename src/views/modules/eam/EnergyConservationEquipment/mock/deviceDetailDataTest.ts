/**
 * 设备详情弹窗折线图数据测试文件
 * 用于验证数据生成和格式的正确性
 * 注意：此文件仅用于开发测试，生产环境中应删除
 */

import { 
  getDeviceDetailData, 
  deviceDetailHourlyData, 
  deviceDetailDailyData, 
  deviceDetailMonthlyData,
  DeviceDetailChartPoint 
} from './chartTrendData';

// 数据验证函数
const validateDeviceDetailData = (data: DeviceDetailChartPoint[], timeRange: string) => {
  console.log(`\n=== 验证设备详情数据 (${timeRange}) ===`);
  console.log(`数据点数量: ${data.length}`);
  
  if (data.length === 0) {
    console.error('❌ 数据为空');
    return false;
  }

  // 验证数据结构
  const hasValidStructure = data.every(item => 
    typeof item.date === 'string' &&
    typeof item.onlineCount === 'number' &&
    typeof item.offlineCount === 'number'
  );
  
  if (!hasValidStructure) {
    console.error('❌ 数据结构不正确');
    return false;
  }

  // 验证数据范围
  const onlineCounts = data.map(item => item.onlineCount);
  const offlineCounts = data.map(item => item.offlineCount);
  
  const minOnline = Math.min(...onlineCounts);
  const maxOnline = Math.max(...onlineCounts);
  const minOffline = Math.min(...offlineCounts);
  const maxOffline = Math.max(...offlineCounts);

  console.log(`在线数量范围: ${minOnline} - ${maxOnline}`);
  console.log(`离线数量范围: ${minOffline} - ${maxOffline}`);

  // 验证数据合理性
  const hasReasonableData = minOnline >= 0 && minOffline >= 0 && maxOnline <= 20 && maxOffline <= 20;
  
  if (!hasReasonableData) {
    console.error('❌ 数据范围不合理');
    return false;
  }

  // 显示前3个数据点作为示例
  console.log('前3个数据点:');
  data.slice(0, 3).forEach((item, index) => {
    console.log(`  ${index + 1}. ${item.date} - 在线:${item.onlineCount}, 离线:${item.offlineCount}`);
  });

  console.log('✅ 数据验证通过');
  return true;
};

// 测试时间格式
const testTimeFormats = () => {
  console.log('\n=== 测试时间格式 ===');
  
  // 24小时数据 - 应该是 HH:00 格式
  const hourlyData = getDeviceDetailData('24h');
  const hourlyTimeFormat = /^\d{2}:\d{2}$/.test(hourlyData[0]?.date || '');
  console.log(`24小时格式 (${hourlyData[0]?.date}): ${hourlyTimeFormat ? '✅' : '❌'}`);

  // 7天数据 - 应该是 MM/DD 格式
  const dailyData = getDeviceDetailData('7d');
  const dailyTimeFormat = /^\d{2}\/\d{2}$/.test(dailyData[0]?.date || '');
  console.log(`7天格式 (${dailyData[0]?.date}): ${dailyTimeFormat ? '✅' : '❌'}`);

  // 30天数据 - 应该是 MM/DD 格式
  const monthlyData = getDeviceDetailData('30d');
  const monthlyTimeFormat = /^\d{2}\/\d{2}$/.test(monthlyData[0]?.date || '');
  console.log(`30天格式 (${monthlyData[0]?.date}): ${monthlyTimeFormat ? '✅' : '❌'}`);
};

// 测试数据点数量
const testDataPointCounts = () => {
  console.log('\n=== 测试数据点数量 ===');
  
  const hourlyData = getDeviceDetailData('24h');
  console.log(`24小时数据点数量: ${hourlyData.length} (期望: 24) ${hourlyData.length === 24 ? '✅' : '❌'}`);

  const dailyData = getDeviceDetailData('7d');
  console.log(`7天数据点数量: ${dailyData.length} (期望: 7) ${dailyData.length === 7 ? '✅' : '❌'}`);

  const monthlyData = getDeviceDetailData('30d');
  console.log(`30天数据点数量: ${monthlyData.length} (期望: 30) ${monthlyData.length === 30 ? '✅' : '❌'}`);
};

// 测试ECharts配置兼容性
const testEChartsCompatibility = () => {
  console.log('\n=== 测试ECharts配置兼容性 ===');
  
  const testData = getDeviceDetailData('24h');
  
  try {
    // 模拟ECharts配置
    const mockEChartsOption = {
      xAxis: {
        data: testData.map(item => item.date)
      },
      series: [
        {
          name: '在线数量',
          data: testData.map(item => item.onlineCount)
        },
        {
          name: '离线数量',
          data: testData.map(item => item.offlineCount)
        }
      ]
    };

    const hasValidXAxisData = mockEChartsOption.xAxis.data.length > 0;
    const hasValidSeriesData = mockEChartsOption.series.every(s => s.data.length > 0);
    
    console.log(`X轴数据: ${hasValidXAxisData ? '✅' : '❌'}`);
    console.log(`系列数据: ${hasValidSeriesData ? '✅' : '❌'}`);
    console.log('ECharts配置兼容性: ✅');
    
  } catch (error) {
    console.error('❌ ECharts配置兼容性测试失败:', error);
  }
};

// 运行所有测试
export const runDeviceDetailDataTests = () => {
  console.log('🚀 开始设备详情弹窗数据测试...');
  
  // 验证各时间范围的数据
  validateDeviceDetailData(getDeviceDetailData('24h'), '24小时');
  validateDeviceDetailData(getDeviceDetailData('7d'), '7天');
  validateDeviceDetailData(getDeviceDetailData('30d'), '30天');
  
  // 验证直接导出的数据
  validateDeviceDetailData(deviceDetailHourlyData, '小时级别数据');
  validateDeviceDetailData(deviceDetailDailyData, '天级别数据');
  validateDeviceDetailData(deviceDetailMonthlyData, '月级别数据');
  
  // 其他测试
  testTimeFormats();
  testDataPointCounts();
  testEChartsCompatibility();
  
  console.log('\n🎉 设备详情弹窗数据测试完成！');
};

// 导出测试数据用于调试
export const getTestSummary = () => {
  return {
    hourlyDataSample: getDeviceDetailData('24h').slice(0, 3),
    dailyDataSample: getDeviceDetailData('7d').slice(0, 3),
    monthlyDataSample: getDeviceDetailData('30d').slice(0, 3),
    dataCounts: {
      hourly: getDeviceDetailData('24h').length,
      daily: getDeviceDetailData('7d').length,
      monthly: getDeviceDetailData('30d').length
    }
  };
};

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  // Node.js环境
  runDeviceDetailDataTests();
}
