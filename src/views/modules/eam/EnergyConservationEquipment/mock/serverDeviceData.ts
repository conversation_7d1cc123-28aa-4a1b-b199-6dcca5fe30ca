/**
 * 服务器设备模拟数据
 */
import {
  StatisticCard,
  TrendChartConfig,
  ServerDevice,
  DeviceStatus,
  ChartDataPoint
} from '../types';
import { getServerTrendData } from './chartTrendData';

// 服务器设备统计卡片数据
export const serverStatistics: StatisticCard[] = [
  {
    title: '管控设备总数量',
    value: 1560,
    trend: {
      type: 'up',
      value: 12,
      period: '月环比'
    },
    extraInfo: {
      label: '日环比',
      value: '0'
    }
  },
  {
    title: '服务器在线数量',
    value: 1260,
    extraInfo: {
      label: '日均在线量',
      value: '1200'
    },
    chart: {
      type: 'line',
      data: [1100, 1150, 1200, 1180, 1220, 1260, 1240],
      color: '#8B5CF6'
    }
  },
  {
    title: '长期离线服务器数量（超7天）',
    value: 560,
    extraInfo: {
      label: '占比',
      value: '40%'
    },
    chart: {
      type: 'bar',
      data: [520, 540, 580, 560, 590, 560, 555],
      color: '#3B82F6'
    }
  },
  {
    title: '长期离线服务器数量（超7天）',
    value: 78,
    extraInfo: {
      label: '占比',
      value: '10%'
    },
    chart: {
      type: 'progress',
      data: [78],
      color: '#3B82F6',
      percentage: 10
    }
  }
];

// 服务器设备趋势图表配置
export const serverTrendChart: TrendChartConfig = {
  title: '服务器在线趋势',
  data: getServerTrendData('24h'), // 默认显示24小时数据
  filters: {
    platform: ['安全运维平台', '其他平台'],
    location: ['张三', '李四', '王五'],
    timeRange: ['东莞中心医院', '其他医院']
  }
};

// 动态获取服务器趋势图表数据的函数
export const getServerTrendChartData = (timeRange: '24h' | '7d' | '30d' = '24h'): TrendChartConfig => {
  return {
    title: '服务器在线趋势',
    data: getServerTrendData(timeRange),
    filters: {
      platform: ['安全运维平台', '其他平台'],
      location: ['张三', '李四', '王五'],
      timeRange: ['东莞中心医院', '其他医院']
    }
  };
};

// 服务器设备列表数据
export const serverDeviceList: ServerDevice[] = [
  {
    id: '1',
    status: DeviceStatus.ONLINE,
    ipAddress: '************',
    macAddress: '38:D5:7A:E8:62:19',
    businessSystem: '38:D5:7A:E8:62:19',
    assetOwner: '张三',
    location: '主机房',
    onlineDuration: '12h',
    operations: ['查看', '在线状态', '移除']
  },
  {
    id: '2',
    status: DeviceStatus.ONLINE,
    ipAddress: '************',
    macAddress: '38:D5:7A:E8:62:19',
    businessSystem: '38:D5:7A:E8:62:19',
    assetOwner: '李四',
    location: '副机房合平台',
    onlineDuration: '12h',
    operations: ['查看', '在线状态', '移除']
  },
  {
    id: '3',
    status: DeviceStatus.MAINTENANCE,
    ipAddress: '************',
    macAddress: '38:D5:7A:E8:62:19',
    businessSystem: '38:D5:7A:E8:62:19',
    assetOwner: '南五',
    location: '副机房合平台',
    onlineDuration: '12h',
    operations: ['查看', '在线状态', '移除']
  },
  {
    id: '4',
    status: DeviceStatus.FAULT,
    ipAddress: '************',
    macAddress: '38:D5:7A:E8:62:19',
    businessSystem: '38:D5:7A:E8:62:19',
    assetOwner: '老六',
    location: '副机房合平台',
    onlineDuration: '12h',
    operations: ['查看', '在线状态', '移除']
  },
  {
    id: '5',
    status: DeviceStatus.OFFLINE,
    ipAddress: '************',
    macAddress: '38:D5:7A:E8:62:19',
    businessSystem: '38:D5:7A:E8:62:19',
    assetOwner: '小七',
    location: '副机房合平台',
    onlineDuration: '12h',
    operations: ['查看', '在线状态', '移除']
  }
];
