/**
 * 折线图趋势数据模拟文件
 * 包含服务器设备和终端设备的不同时间粒度数据
 * 注意：此文件为开发阶段模拟数据，后续开发完成后需要统一移除
 */
import { ChartDataPoint } from '../types';

// 生成随机数据的工具函数
const generateRandomValue = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

// 生成日期字符串的工具函数
const formatDate = (date: Date, format: 'hour' | 'day' | 'month'): string => {
  switch (format) {
    case 'hour':
      return `${date.getHours().toString().padStart(2, '0')}:00`;
    case 'day':
      return `${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}`;
    case 'month':
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
    default:
      return date.toISOString().split('T')[0];
  }
};

// 生成时间序列数据的工具函数
const generateTimeSeriesData = (
  count: number,
  timeUnit: 'hour' | 'day' | 'month',
  baseOnlineCount: number,
  baseTotalCount: number,
  variance: number = 0.2
): ChartDataPoint[] => {
  const data: ChartDataPoint[] = [];
  const now = new Date();

  for (let i = count - 1; i >= 0; i--) {
    const date = new Date(now);

    switch (timeUnit) {
      case 'hour':
        date.setHours(date.getHours() - i);
        break;
      case 'day':
        date.setDate(date.getDate() - i);
        break;
      case 'month':
        date.setMonth(date.getMonth() - i);
        break;
    }

    // 生成带有随机波动的数据
    const onlineVariance = Math.floor(baseOnlineCount * variance);
    const totalVariance = Math.floor(baseTotalCount * variance);

    const onlineCount = generateRandomValue(
      baseOnlineCount - onlineVariance,
      baseOnlineCount + onlineVariance
    );
    const totalCount = generateRandomValue(
      baseTotalCount - totalVariance,
      baseTotalCount + totalVariance
    );

    // 确保在线数量不超过总数量
    const finalOnlineCount = Math.min(onlineCount, totalCount);

    data.push({
      date: formatDate(date, timeUnit),
      onlineCount: finalOnlineCount,
      totalCount: totalCount
    });
  }

  return data;
};

// ==================== 服务器设备折线图数据 ====================

// 服务器设备 - 小时级别数据（24小时）
export const serverHourlyTrendData: ChartDataPoint[] = generateTimeSeriesData(
  24, // 24个小时
  'hour',
  1200, // 基础在线数量
  1560, // 基础总数量
  0.15  // 15%的波动范围
);

// 服务器设备 - 天级别数据（30天）
export const serverDailyTrendData: ChartDataPoint[] = generateTimeSeriesData(
  30, // 30天
  'day',
  1180, // 基础在线数量
  1560, // 基础总数量
  0.25  // 25%的波动范围
);

// 服务器设备 - 月级别数据（12个月）
export const serverMonthlyTrendData: ChartDataPoint[] = generateTimeSeriesData(
  12, // 12个月
  'month',
  1100, // 基础在线数量
  1500, // 基础总数量（考虑设备增长）
  0.3   // 30%的波动范围
);

// ==================== 终端设备折线图数据 ====================

// 终端设备 - 小时级别数据（24小时）
export const terminalHourlyTrendData: ChartDataPoint[] = generateTimeSeriesData(
  24, // 24个小时
  'hour',
  1850, // 基础在线数量
  2340, // 基础总数量
  0.12  // 12%的波动范围
);

// 终端设备 - 天级别数据（30天）
export const terminalDailyTrendData: ChartDataPoint[] = generateTimeSeriesData(
  30, // 30天
  'day',
  1820, // 基础在线数量
  2340, // 基础总数量
  0.2   // 20%的波动范围
);

// 终端设备 - 月级别数据（12个月）
export const terminalMonthlyTrendData: ChartDataPoint[] = generateTimeSeriesData(
  12, // 12个月
  'month',
  1750, // 基础在线数量
  2200, // 基础总数量（考虑设备增长）
  0.25  // 25%的波动范围
);

// ==================== 数据导出对象 ====================

// 服务器设备所有时间粒度数据
export const serverTrendDataCollection = {
  hourly: serverHourlyTrendData,
  daily: serverDailyTrendData,
  monthly: serverMonthlyTrendData
};

// 终端设备所有时间粒度数据
export const terminalTrendDataCollection = {
  hourly: terminalHourlyTrendData,
  daily: terminalDailyTrendData,
  monthly: terminalMonthlyTrendData
};

// 根据时间范围获取服务器数据的工具函数
export const getServerTrendData = (timeRange: '24h' | '7d' | '30d'): ChartDataPoint[] => {
  switch (timeRange) {
    case '24h':
      return serverHourlyTrendData;
    case '7d':
      return serverDailyTrendData.slice(-7); // 最近7天
    case '30d':
      return serverDailyTrendData; // 最近30天
    default:
      return serverHourlyTrendData;
  }
};

// 根据时间范围获取终端数据的工具函数
export const getTerminalTrendData = (timeRange: '24h' | '7d' | '30d'): ChartDataPoint[] => {
  switch (timeRange) {
    case '24h':
      return terminalHourlyTrendData;
    case '7d':
      return terminalDailyTrendData.slice(-7); // 最近7天
    case '30d':
      return terminalDailyTrendData; // 最近30天
    default:
      return terminalHourlyTrendData;
  }
};

// 统一的数据获取函数
export const getTrendDataByDeviceType = (
  deviceType: 'server' | 'terminal',
  timeRange: '24h' | '7d' | '30d'
): ChartDataPoint[] => {
  if (deviceType === 'server') {
    return getServerTrendData(timeRange);
  } else {
    return getTerminalTrendData(timeRange);
  }
};

// ==================== 设备详情弹窗折线图数据 ====================

// 设备详情图表数据点接口（在线/离线数量）
export interface DeviceDetailChartPoint {
  date: string;
  onlineCount: number;
  offlineCount: number;
}

// 生成设备详情图表数据的工具函数
const generateDeviceDetailData = (
  count: number,
  timeUnit: 'hour' | 'day' | 'month',
  baseOnlineCount: number,
  baseOfflineCount: number,
  variance: number = 0.3
): DeviceDetailChartPoint[] => {
  const data: DeviceDetailChartPoint[] = [];
  const now = new Date();

  for (let i = count - 1; i >= 0; i--) {
    const date = new Date(now);

    switch (timeUnit) {
      case 'hour':
        date.setHours(date.getHours() - i);
        break;
      case 'day':
        date.setDate(date.getDate() - i);
        break;
      case 'month':
        date.setMonth(date.getMonth() - i);
        break;
    }

    // 生成带有随机波动的数据
    const onlineVariance = Math.floor(baseOnlineCount * variance);
    const offlineVariance = Math.floor(baseOfflineCount * variance);

    const onlineCount = generateRandomValue(
      Math.max(1, baseOnlineCount - onlineVariance),
      baseOnlineCount + onlineVariance
    );
    const offlineCount = generateRandomValue(
      Math.max(0, baseOfflineCount - offlineVariance),
      baseOfflineCount + offlineVariance
    );

    data.push({
      date: formatDate(date, timeUnit),
      onlineCount,
      offlineCount
    });
  }

  return data;
};

// 设备详情 - 小时级别数据（24小时）
export const deviceDetailHourlyData: DeviceDetailChartPoint[] = generateDeviceDetailData(
  24, // 24个小时
  'hour',
  5,  // 基础在线数量
  2,  // 基础离线数量
  0.4 // 40%的波动范围
);

// 设备详情 - 天级别数据（7天）
export const deviceDetailDailyData: DeviceDetailChartPoint[] = generateDeviceDetailData(
  7,  // 7天
  'day',
  6,  // 基础在线数量
  2,  // 基础离线数量
  0.3 // 30%的波动范围
);

// 设备详情 - 月级别数据（30天）
export const deviceDetailMonthlyData: DeviceDetailChartPoint[] = generateDeviceDetailData(
  30, // 30天
  'day',
  5,  // 基础在线数量
  3,  // 基础离线数量
  0.5 // 50%的波动范围
);

// 根据时间范围获取设备详情数据的工具函数
export const getDeviceDetailData = (timeRange: '24h' | '7d' | '30d'): DeviceDetailChartPoint[] => {
  switch (timeRange) {
    case '24h':
      return deviceDetailHourlyData;
    case '7d':
      return deviceDetailDailyData;
    case '30d':
      return deviceDetailMonthlyData;
    default:
      return deviceDetailHourlyData;
  }
};
