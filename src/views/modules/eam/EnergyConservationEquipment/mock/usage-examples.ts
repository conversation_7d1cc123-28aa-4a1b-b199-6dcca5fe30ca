/**
 * 模拟数据使用示例
 * 此文件展示如何在不同场景下使用模拟数据
 * 注意：此文件仅用于开发参考，生产环境中应删除
 */

import {
  serverTrendDataCollection,
  terminalTrendDataCollection,
  getTrendDataByDeviceType,
  getServerTrendData,
  getTerminalTrendData
} from './chartTrendData';

import { getServerTrendChartData } from './serverDeviceData';
import { getTerminalTrendChartData } from './terminalDeviceData';

// ==================== 基础数据获取示例 ====================

/**
 * 示例1: 获取特定设备类型和时间范围的数据
 */
export const example1_BasicDataRetrieval = () => {
  // 获取服务器24小时数据
  const server24h = getTrendDataByDeviceType('server', '24h');
  console.log('服务器24小时数据:', server24h);

  // 获取终端7天数据
  const terminal7d = getTrendDataByDeviceType('terminal', '7d');
  console.log('终端7天数据:', terminal7d);

  // 获取服务器30天数据
  const server30d = getTrendDataByDeviceType('server', '30d');
  console.log('服务器30天数据:', server30d);
};

/**
 * 示例2: 直接访问数据集合
 */
export const example2_DirectDataAccess = () => {
  // 访问服务器所有时间粒度数据
  console.log('服务器小时数据:', serverTrendDataCollection.hourly);
  console.log('服务器天数据:', serverTrendDataCollection.daily);
  console.log('服务器月数据:', serverTrendDataCollection.monthly);

  // 访问终端所有时间粒度数据
  console.log('终端小时数据:', terminalTrendDataCollection.hourly);
  console.log('终端天数据:', terminalTrendDataCollection.daily);
  console.log('终端月数据:', terminalTrendDataCollection.monthly);
};

// ==================== 图表配置示例 ====================

/**
 * 示例3: 获取完整的图表配置数据
 */
export const example3_ChartConfiguration = () => {
  // 获取服务器图表配置（包含标题、数据、过滤器）
  const serverChartConfig = getServerTrendChartData('24h');
  console.log('服务器图表配置:', serverChartConfig);

  // 获取终端图表配置
  const terminalChartConfig = getTerminalTrendChartData('7d');
  console.log('终端图表配置:', terminalChartConfig);
};

// ==================== 动态数据更新示例 ====================

/**
 * 示例4: 模拟时间范围切换
 */
export const example4_TimeRangeSwitch = () => {
  const timeRanges: ('24h' | '7d' | '30d')[] = ['24h', '7d', '30d'];
  
  timeRanges.forEach(range => {
    const serverData = getServerTrendData(range);
    const terminalData = getTerminalTrendData(range);
    
    console.log(`${range} 服务器数据点数量:`, serverData.length);
    console.log(`${range} 终端数据点数量:`, terminalData.length);
    
    // 显示数据范围
    if (serverData.length > 0) {
      console.log(`${range} 服务器数据时间范围:`, 
        serverData[0].date, '到', serverData[serverData.length - 1].date);
    }
  });
};

// ==================== Vue组件中的使用示例 ====================

/**
 * 示例5: 在Vue组件中使用（伪代码）
 */
export const example5_VueComponentUsage = `
// 在Vue组件的setup函数中
import { ref, onMounted } from 'vue';
import { getTrendDataByDeviceType } from '../mock/chartTrendData';

export default {
  setup() {
    const chartData = ref([]);
    const currentTimeRange = ref('24h');
    const currentDeviceType = ref('server');

    // 加载图表数据
    const loadChartData = () => {
      chartData.value = getTrendDataByDeviceType(
        currentDeviceType.value,
        currentTimeRange.value
      );
    };

    // 切换时间范围
    const switchTimeRange = (range) => {
      currentTimeRange.value = range;
      loadChartData();
    };

    // 切换设备类型
    const switchDeviceType = (type) => {
      currentDeviceType.value = type;
      loadChartData();
    };

    onMounted(() => {
      loadChartData();
    });

    return {
      chartData,
      currentTimeRange,
      currentDeviceType,
      switchTimeRange,
      switchDeviceType
    };
  }
};
`;

// ==================== ECharts集成示例 ====================

/**
 * 示例6: ECharts图表配置
 */
export const example6_EChartsIntegration = () => {
  const data = getTrendDataByDeviceType('server', '24h');
  
  const echartsOption = {
    title: {
      text: '服务器在线趋势'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['在线数量', '总数量']
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.date)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '在线数量',
        type: 'line',
        data: data.map(item => item.onlineCount),
        smooth: true
      },
      {
        name: '总数量',
        type: 'line',
        data: data.map(item => item.totalCount),
        smooth: true
      }
    ]
  };

  return echartsOption;
};

// ==================== 数据验证示例 ====================

/**
 * 示例7: 数据完整性验证
 */
export const example7_DataValidation = () => {
  const validateData = (data: any[], deviceType: string, timeRange: string) => {
    console.log(`验证 ${deviceType} ${timeRange} 数据:`);
    console.log('- 数据点数量:', data.length);
    console.log('- 数据结构完整性:', data.every(item => 
      item.hasOwnProperty('date') && 
      item.hasOwnProperty('onlineCount') && 
      item.hasOwnProperty('totalCount')
    ));
    console.log('- 在线数量不超过总数量:', data.every(item => 
      item.onlineCount <= item.totalCount
    ));
    console.log('- 数据类型正确:', data.every(item => 
      typeof item.onlineCount === 'number' && 
      typeof item.totalCount === 'number'
    ));
  };

  // 验证所有数据集
  validateData(getServerTrendData('24h'), '服务器', '24小时');
  validateData(getServerTrendData('7d'), '服务器', '7天');
  validateData(getServerTrendData('30d'), '服务器', '30天');
  
  validateData(getTerminalTrendData('24h'), '终端', '24小时');
  validateData(getTerminalTrendData('7d'), '终端', '7天');
  validateData(getTerminalTrendData('30d'), '终端', '30天');
};

// ==================== 导出所有示例 ====================

export const runAllExamples = () => {
  console.log('=== 运行所有模拟数据使用示例 ===');
  
  example1_BasicDataRetrieval();
  example2_DirectDataAccess();
  example3_ChartConfiguration();
  example4_TimeRangeSwitch();
  example7_DataValidation();
  
  console.log('=== 示例运行完成 ===');
};
