/**
 * 终端设备模拟数据
 */
import {
  StatisticCard,
  TrendChartConfig,
  TerminalDevice,
  DeviceStatus,
  ChartDataPoint
} from '../types';
import { getTerminalTrendData } from './chartTrendData';

// 终端设备统计卡片数据
export const terminalStatistics: StatisticCard[] = [
  {
    title: '管控设备总数量',
    value: 2340,
    trend: {
      type: 'up',
      value: 18,
      period: '月环比'
    },
    extraInfo: {
      label: '日环比',
      value: '5'
    }
  },
  {
    title: '终端在线数量',
    value: 1890,
    extraInfo: {
      label: '日均在线量',
      value: '1850'
    },
    chart: {
      type: 'line',
      data: [1800, 1850, 1900, 1880, 1920, 1890, 1895],
      color: '#8B5CF6'
    }
  },
  {
    title: '长期离线终端数量（超7天）',
    value: 450,
    extraInfo: {
      label: '占比',
      value: '19%'
    },
    chart: {
      type: 'bar',
      data: [420, 440, 460, 450, 470, 450, 445],
      color: '#3B82F6'
    }
  },
  {
    title: '长期离线终端数量（超7天）',
    value: 125,
    extraInfo: {
      label: '占比',
      value: '5%'
    },
    chart: {
      type: 'progress',
      data: [125],
      color: '#3B82F6',
      percentage: 5
    }
  }
];

// 终端设备趋势图表配置
export const terminalTrendChart: TrendChartConfig = {
  title: '终端在线趋势',
  data: getTerminalTrendData('24h'), // 默认显示24小时数据
  filters: {
    platform: ['张三', '李四', '王五'],
    location: ['东莞中心医院', '其他医院'],
    timeRange: ['近24小时', '近7天', '近30天']
  }
};

// 动态获取终端趋势图表数据的函数
export const getTerminalTrendChartData = (timeRange: '24h' | '7d' | '30d' = '24h'): TrendChartConfig => {
  return {
    title: '终端在线趋势',
    data: getTerminalTrendData(timeRange),
    filters: {
      platform: ['张三', '李四', '王五'],
      location: ['东莞中心医院', '其他医院'],
      timeRange: ['近24小时', '近7天', '近30天']
    }
  };
};

// 终端设备列表数据
export const terminalDeviceList: TerminalDevice[] = [
  {
    id: '1',
    status: DeviceStatus.ONLINE,
    ipAddress: '************',
    macAddress: '38:D5:7A:E8:62:19',
    assetOwner: '张三',
    location: '护士站',
    nearestOnlineTime: '12h',
    lastOnlineTime: '2016-09-21 08:50:08',
    lastOnlineDuration: '10天2小时5分25秒',
    operations: ['查看', '在线状态', '移除']
  },
  {
    id: '2',
    status: DeviceStatus.ONLINE,
    ipAddress: '************',
    macAddress: '38:D5:7A:E8:62:19',
    assetOwner: '李四',
    location: '护士站',
    nearestOnlineTime: '12h',
    lastOnlineTime: '2016-09-21 08:50:08',
    lastOnlineDuration: '10天2小时5分25秒',
    operations: ['查看', '在线状态', '移除']
  },
  {
    id: '3',
    status: DeviceStatus.MAINTENANCE,
    ipAddress: '************',
    macAddress: '38:D5:7A:E8:62:19',
    assetOwner: '南五',
    location: '护士站',
    nearestOnlineTime: '12h',
    lastOnlineTime: '2016-09-21 08:50:08',
    lastOnlineDuration: '10天2小时5分25秒',
    operations: ['查看', '在线状态', '移除']
  },
  {
    id: '4',
    status: DeviceStatus.OFFLINE,
    ipAddress: '************',
    macAddress: '38:D5:7A:E8:62:19',
    assetOwner: '老六',
    location: '护士站',
    nearestOnlineTime: '12h',
    lastOnlineTime: '2016-09-21 08:50:08',
    lastOnlineDuration: '10天2小时5分25秒',
    operations: ['查看', '在线状态', '移除']
  },
  {
    id: '5',
    status: DeviceStatus.OFFLINE,
    ipAddress: '************',
    macAddress: '38:D5:7A:E8:62:19',
    assetOwner: '小七',
    location: '护士站',
    nearestOnlineTime: '12h',
    lastOnlineTime: '2016-09-21 08:50:08',
    lastOnlineDuration: '10天2小时5分25秒',
    operations: ['查看', '在线状态', '移除']
  }
];
