<template>
  <div class="network-device-monitor">
    <!-- 使用splitpane组件，设置左右分割的比例 -->
    <splitpane :splitSet="settingLR">
      <!-- 左侧面板 -->
      <template #paneL>
        <div class="left-panel">
          <div class="org-header">
            <h3>所属组织</h3>
          </div>
          <div class="org-tree">
            <el-input
              v-model="orgSearchKeyword"
              placeholder="请输入"
              prefix-icon="Search"
              size="small"
              clearable
            />
            <el-tree
              ref="orgTreeRef"
              :data="orgTreeData"
              :props="{ children: 'children', label: 'name' }"
              node-key="id"
              :default-expanded-keys="['1']"
              highlight-current
              :expand-on-click-node="false"
              :filter-node-method="filterOrgNode"
              :style="treeStyle"
              @node-click="handleOrgNodeClick"
            />
          </div>
        </div>
      </template>

      <!-- 右侧面板 -->
      <template #paneR>
        <div class="main-content">
      <!-- Tab切换 - 按原型图样式 -->
      <div class="tab-container">
        <div
          v-for="tab in tabConfigs"
          :key="tab.key"
          :class="['tab-item', { active: state.activeTab === tab.key }]"
          @click="switchTab(tab.key)"
        >
          {{ tab.label }}
        </div>
      </div>

      <!-- 统计卡片区域 -->
      <div class="statistics-section">
        <div
          v-for="(stat, index) in (displayStatistics || [])"
          :key="index"
          class="stat-card"
        >
          <div class="stat-header">
            <span class="stat-title">{{ stat.title }}</span>
            <el-icon v-if="stat.title.includes('超7天')" class="help-icon">
              <QuestionFilled />
            </el-icon>
          </div>
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-footer">
            <!-- 趋势信息 -->
            <div v-if="stat.trend" class="stat-trend">
              <span class="trend-label">{{ stat.trend.period }}:</span>
              <span :class="['trend-value', stat.trend.type]">
                {{ stat.trend.type === 'up' ? '+' : '' }}{{ stat.trend.value }}
              </span>
            </div>
            <!-- 额外信息（如日均在线量、占比等） -->
            <div v-if="stat.extraInfo" class="stat-extra">
              <span class="extra-label">{{ stat.extraInfo.label }}:</span>
              <span class="extra-value">{{ stat.extraInfo.value }}</span>
            </div>
            <!-- 图表区域 -->
            <div v-if="stat.chart" class="stat-chart">
              <div
                v-if="stat.chart.type === 'line'"
                class="mini-line-chart"
              >
                <svg
                  width="100%"
                  height="20"
                  viewBox="0 0 100 20"
                  preserveAspectRatio="none"
                >
                  <!-- 渐变定义 -->
                  <defs>
                    <linearGradient :id="`gradient-${index}`" x1="0%" y1="0%" x2="0%" y2="100%">
                      <stop offset="0%" :stop-color="stat.chart.color" stop-opacity="0.3"/>
                      <stop offset="100%" :stop-color="stat.chart.color" stop-opacity="0.1"/>
                    </linearGradient>
                  </defs>
                  <!-- 面积填充 -->
                  <path
                    :d="generateAreaPath(stat.chart.data)"
                    :fill="`url(#gradient-${index})`"
                  />
                  <!-- 折线 -->
                  <polyline
                    :points="generateLinePoints(stat.chart.data)"
                    fill="none"
                    :stroke="stat.chart.color"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <!-- 数据点（仅在悬停时显示） -->
                  <g class="data-points" style="opacity: 0;">
                    <circle
                      v-for="(point, pointIndex) in generateDataPoints(stat.chart.data)"
                      :key="pointIndex"
                      :cx="point.x"
                      :cy="point.y"
                      r="1.5"
                      :fill="stat.chart.color"
                      stroke="white"
                      stroke-width="0.5"
                    />
                  </g>
                </svg>
              </div>
              <div
                v-else-if="stat.chart.type === 'bar'"
                class="mini-bar-chart"
              >
                <div
                  v-for="(value, i) in stat.chart.data.slice(-7)"
                  :key="i"
                  class="bar-item"
                  :style="{
                    height: `${(value / Math.max(...stat.chart.data)) * 100}%`,
                    backgroundColor: stat.chart.color
                  }"
                ></div>
              </div>
              <div
                v-else-if="stat.chart.type === 'progress'"
                class="mini-progress-chart"
              >
                <div class="progress-bg">
                  <div
                    class="progress-fill"
                    :style="{
                      width: `${stat.chart.percentage}%`,
                      backgroundColor: stat.chart.color
                    }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 趋势图表区域 -->
      <div class="chart-section">
        <div class="chart-header">
          <h3>{{ currentTrendChart.title || '设备在线趋势' }}</h3>
          <div class="chart-filters">
            <el-select v-model="chartFilters.platform" placeholder="请选择" size="small">
              <el-option
                v-for="option in (currentTrendChart.filters?.platform || [])"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
            <el-select v-model="chartFilters.location" placeholder="请选择" size="small">
              <el-option
                v-for="option in (currentTrendChart.filters?.location || [])"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
            <el-select v-model="chartFilters.timeRange" placeholder="请选择" size="small">
              <el-option
                v-for="option in (currentTrendChart.filters?.timeRange || [])"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
            <el-select v-model="chartFilters.period" placeholder="近24小时" size="small">
              <el-option label="近24小时" value="24h" />
              <el-option label="近7天" value="7d" />
              <el-option label="近30天" value="30d" />
            </el-select>
          </div>
        </div>
        <div class="chart-content">
          <!-- 这里集成ECharts图表组件 -->
          <div class="trend-chart" ref="trendChartRef"></div>
        </div>
      </div>

      <!-- 设备列表区域 -->
      <div class="device-list-section">
        <div class="list-header">
          <h3>{{ state.activeTab === 'server' ? '服务器列表' : '终端列表' }}</h3>
        </div>

        <!-- 设备表格 -->
        <im-table
          ref="tableRef"
          :data="tableData"
          :columns="currentTableColumns"
          center
          toolbar
          :table-alert="{
            closable: false
          }"
          :operator="operatorConfig"
          :height="tableOption.height"
          :stripe="tableOption.stripe"
          show-checkbox
          :pagination="tablePage"
          :loading="tableLoading"
          :filter-data-provider="filterDataProvider"
          @on-reload="resetTablePageAndQuery"
          @selection-change="selectionChangeHandler"
          @on-page-change="loadDeviceList"
        >
          <!-- 工具栏左侧搜索组件 -->
          <template #toolbar-left>
            <search-with-column
              v-model="columnCondition.value"
              v-model:fuzzy-enable="columnCondition.fuzzyable"
              v-model:column-val="columnCondition.field"
              :column-options="columnSearchOptions"
              :column-select-width="90"
              @search="handleSearch"
              @reset="handleReset"
              input-class-name="w-80"
            />
          </template>

          <!-- 工具栏右侧操作按钮 -->
          <template #toolbar-right="toolbarSlot">
            <div class="flex items-center gap-2 pr-6">
              <el-button type="primary" size="small">
                <el-icon><Plus /></el-icon>
                新增资产
              </el-button>
              <el-button
                size="small"
                :disabled="toolbarSlot.checkedRows.length === 0"
              >
                批量删除
              </el-button>
              <el-button size="small" @click="showTaskConfig">任务配置</el-button>
              <el-button size="small">导出</el-button>
            </div>
          </template>
          <!-- 表格操作按钮 -->
          <template #operator="scope: any">
            <el-button
              v-for="operation in (scope.row.operations || [])"
              :key="operation"
              :size="scope.size"
              type="primary"
              text
              :icon="operation === '查看' ? useRenderIcon('EP-View') : undefined"
              @click="handleOperation(operation, scope.row)"
            >
              {{ operation === '查看' ? '查看' : operation }}
            </el-button>
          </template>

          <!-- 状态列 -->
          <template #status="{ row }">
            <div class="status-tag-container">
              <el-tag
                :type="getStatusTagType(row.status)"
                size="small"
                class="status-tag"
              >
                {{ getStatusText(row.status) }}
              </el-tag>
            </div>
          </template>
        </im-table>
      </div>
        </div>
      </template>
    </splitpane>

    <!-- 设备详情弹窗 -->
    <el-dialog
      v-model="deviceDetailVisible"
      title="终端在线趋势"
      width="70%"
      top="5vh"
      :before-close="handleCloseDetail"
      class="device-detail-dialog"
    >
      <div class="device-detail-content">
        <!-- 图表区域 -->
        <div class="chart-section">
          <div class="chart-header">
            <div class="chart-controls">
              <el-button type="primary" size="small">保存</el-button>
              <el-button type="primary" size="small">关闭</el-button>
            </div>
            <div class="time-selector">
              <el-select v-model="selectedTimeRange" size="small" style="width: 120px;">
                <el-option label="近24小时" value="24h" />
                <el-option label="近7天" value="7d" />
                <el-option label="近30天" value="30d" />
              </el-select>
            </div>
          </div>
          <div class="trend-chart" ref="detailTrendChartRef"></div>
        </div>

        <!-- 搜索区域 -->
        <div class="search-section">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入"
            size="small"
            style="width: 200px;"
            clearable
          >
            <template #append>
              <el-button type="primary" @click="searchDevices">查询</el-button>
            </template>
          </el-input>
        </div>

        <!-- 设备列表表格 -->
        <div class="device-list-section">
          <el-table
            :data="deviceListData"
            style="width: 100%"
            height="250"
            @selection-change="handleDeviceSelection"
          >
            <el-table-column type="selection" minWidth="55" />
            <el-table-column prop="collectTime" label="采集时间" minWidth="150" />
            <el-table-column prop="status" label="在线状态" minWidth="100">
              <template #default="{ row }">
                <el-tag :type="row.status === '在线' ? 'success' : 'danger'" size="small">
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="ipAddress" label="IP地址" minWidth="140" />
            <el-table-column prop="macAddress" label="MAC地址" minWidth="160" />
            <el-table-column prop="lastOnlineTime" label="最后一次在线时间" minWidth="180" />
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="deviceListPage.current"
              v-model:page-size="deviceListPage.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="deviceListPage.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleDevicePageSizeChange"
              @current-change="handleDevicePageChange"
            />
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 任务配置弹窗 -->
    <el-dialog
      v-model="taskConfigVisible"
      title="任务规则配置"
      width="60%"
      top="5vh"
      :before-close="handleCloseTaskConfig"
      class="task-config-dialog"
    >
      <div class="task-config-content">
        <!-- 扫描方式 -->
        <div class="config-row">
          <label class="config-label">扫描方式：</label>
          <div class="config-content">
            <el-checkbox v-model="taskConfig.nmap" label="nmap" />
            <el-checkbox v-model="taskConfig.masscan" label="masscan" />
            <el-checkbox v-model="taskConfig.ping" label="ping" />
          </div>
        </div>

        <!-- 执行周期 -->
        <div class="config-row">
          <label class="config-label">执行周期：</label>
          <div class="config-content">
            <el-checkbox v-model="taskConfig.enableSchedule" label="定期执行" />
            <div v-if="taskConfig.enableSchedule" class="schedule-config">
              <simple-schedule
                ref="simpleScheduleRef"
                v-model:exp="taskConfig.schedulingCycle"
                :enable-minute="true"
                :min-minute="5"
                :enable-hour="true"
                action-type="H"
              />
            </div>
            <div class="one-time-config">
              <el-checkbox v-model="taskConfig.executeOnce" label="执行一次" />
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseTaskConfig">关闭</el-button>
          <el-button type="primary" @click="handleSaveTaskConfig">保存</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount, nextTick, watch, CSSProperties } from 'vue';
import { ElMessage, ElTree } from 'element-plus';
import { QuestionFilled, Plus } from '@element-plus/icons-vue';
import * as echarts from 'echarts';
import splitpane, { ContextProps } from "@/components/ReSplitPane";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import SearchWithColumn from "@/components/Search/SearchWithColumnAddSelect.vue";
import ImTable from "@/components/ItsmCommon/table/ImTable.vue";
import SimpleSchedule from "@/components/SimpleSchedule/SimpleSchedule.vue";
import {
  HeaderFilterValue,
  ImTableInstance
} from "@/components/ItsmCommon";
import {
  FilterOption,
  TableFilterDataProvider
} from "@/components/ItsmCommon/table/props";
import { defaultPageSize, pageSizeOptions } from "@/utils/page_util";
import {
  DeviceTabType,
  DeviceStatus,
  StatisticCard,
  TrendChartConfig,
  HomePageState,
  OrgTreeNode
} from '../types';
import apiService from '../api';
import { getDeviceDetailData } from '../mock/chartTrendData';

// splitpane 配置
const settingLR: ContextProps = reactive({
  minPercent: 10,
  defaultPercent: 20,
  split: "vertical"
});

// 组件引用
const orgTreeRef = ref<InstanceType<typeof ElTree> | null>(null);
const tableRef = ref<ImTableInstance>();
const simpleScheduleRef = ref<InstanceType<typeof SimpleSchedule> | null>(null);

// 响应式数据
const state = reactive<HomePageState>({
  activeTab: DeviceTabType.SERVER,
  serverDevices: [],
  terminalDevices: [],
  tableFilters: {
    searchKeyword: ''
  },
  pagination: {
    current: 1,
    pageSize: 20,
    total: 0
  },
  loading: false
});

// 其他响应式数据
const orgSearchKeyword = ref('');
const chartFilters = reactive({
  platform: '',
  location: '',
  timeRange: '',
  period: '24h'
});

// 搜索相关状态
const columnCondition = reactive({
  value: null,
  field: "ipAddress", // 默认搜索IP地址
  fuzzyable: true,
  operator: "fuzzy"
});

const columnSearchOptions = ref([
  { label: "IP地址", value: "ipAddress" },
  { label: "MAC地址", value: "macAddress" },
  { label: "资产责任人", value: "assetOwner" },
  { label: "所在位置", value: "location" }
]);

// 表格相关状态
const tableData = ref([]);
const tableLoading = ref(false);
const tablePage = reactive({
  align: "right",
  total: 0,
  current: 1,
  currentPage: 1,
  pageSize: defaultPageSize,
  pageSizes: pageSizeOptions
});

// 表格列配置
const serverTableColumns = ref([
  { prop: 'ipAddress', label: 'IP地址', minWidth: 140, sortable: true },
  { prop: 'macAddress', label: 'MAC地址', minWidth: 160, sortable: true },
  { prop: 'businessSystem', label: '所属业务系统', minWidth: 160, sortable: true },
  { prop: 'assetOwner', label: '责任人', minWidth: 120, sortable: true },
  { prop: 'location', label: '所在位置', minWidth: 140, sortable: true },
  { prop: 'onlineDuration', label: '日均在线时长', minWidth: 120, sortable: true },
  { prop: 'status', label: '状态', minWidth: 100, slot: 'status', align: 'center' }
]);

const terminalTableColumns = ref([
  { prop: 'ipAddress', label: 'IP地址', minWidth: 140, sortable: true },
  { prop: 'macAddress', label: 'MAC地址', minWidth: 160, sortable: true },
  { prop: 'assetOwner', label: '资产责任人', minWidth: 120, sortable: true },
  { prop: 'location', label: '所在位置', minWidth: 140, sortable: true },
  { prop: 'avgOnlineDuration', label: '近一月日均在线时长', minWidth: 160, sortable: true },
  { prop: 'lastOnlineDuration', label: '最近在线时长', minWidth: 120, sortable: true },
  { prop: 'lastOnlineTime', label: '最后一次在线时间', minWidth: 180, sortable: true },
  { prop: 'status', label: '状态', minWidth: 100, slot: 'status', align: 'center' }
]);

const currentTableColumns = computed(() => {
  return state.activeTab === DeviceTabType.SERVER
    ? serverTableColumns.value
    : terminalTableColumns.value;
});

const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  selection: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  height: 600,
  rowKey: "id"
});

// 操作列配置
const operatorConfig = reactive({
  label: "操作",
  fixed: "right",
  width: 180, // 增加操作列宽度以容纳更多按钮
  align: "center"
});

// 添加过滤器状态
const filters = ref([]);

// 统计数据
const serverStatistics = ref<StatisticCard[]>([]);
const terminalStatistics = ref<StatisticCard[]>([]);
const serverTrendChart = ref<TrendChartConfig>({
  title: '服务器在线趋势',
  data: [],
  filters: {
    platform: [],
    location: [],
    timeRange: []
  }
});
const terminalTrendChart = ref<TrendChartConfig>({
  title: '终端在线趋势',
  data: [],
  filters: {
    platform: [],
    location: [],
    timeRange: []
  }
});

// 图表引用
const trendChartRef = ref<HTMLElement>();
const detailTrendChartRef = ref<HTMLElement>();
let chartInstance: echarts.ECharts | null = null;
let detailTrendChartInstance: echarts.ECharts | null = null;

// 事件监听器引用
let chartResizeHandler: (() => void) | null = null;
let detailTrendChartResizeHandler: (() => void) | null = null;

// 设备详情弹窗相关
const deviceDetailVisible = ref(false);
const selectedTimeRange = ref('24h');
const searchKeyword = ref('');
const deviceListData = ref([]);
const deviceListPage = reactive({
  current: 1,
  pageSize: 10,
  total: 0
});

// 任务配置弹窗相关
const taskConfigVisible = ref(false);
const taskConfig = reactive({
  nmap: true,
  masscan: false,
  ping: false,
  enableSchedule: true,
  executeOnce: false,
  schedulingCycle: '1H'
});

// 组织架构数据
const orgTreeData = ref<OrgTreeNode[]>([
  {
    id: '1',
    name: '东莞市中心医院',
    children: [
      { id: '1-1', name: '东莞中心医院总院' },
      { id: '1-2', name: '东莞中心医院分院' }
    ]
  }
]);

// Tab配置
const tabConfigs = [
  { key: DeviceTabType.SERVER, label: '服务器设备' },
  { key: DeviceTabType.TERMINAL, label: '终端设备' }
];

// 计算属性
const currentStatistics = computed(() => {
  return state.activeTab === DeviceTabType.SERVER ? serverStatistics.value : terminalStatistics.value;
});

const currentTrendChart = computed(() => {
  return state.activeTab === DeviceTabType.SERVER ? serverTrendChart.value : terminalTrendChart.value;
});

// 新增的计算属性，用于处理和显示统计卡片数据
const displayStatistics = computed(() => {
  const stats = currentStatistics.value;
  if (!stats || !stats.length) return [];

  const isServer = state.activeTab === DeviceTabType.SERVER;

  const serverTitles = [
    '管控服务器数量',
    '服务器在线数量',
    '长期在线服务器数 (超7天)',
    '长期离线服务器数 (超7天)'
  ];

  const terminalTitles = [
    '管控终端设备数量',
    '设备在线数量',
    '长期在线终端数 (超7天)',
    '长期离线设备数量 (超7天)'
  ];

  const titles = isServer ? serverTitles : terminalTitles;

  return stats.map((stat, index) => {
    // Deep copy to avoid modifying original state
    const newStat = JSON.parse(JSON.stringify(stat));

    if (titles[index]) {
      newStat.title = titles[index];
    }

    if (index === 1 && newStat.extraInfo) {
      if (isServer) {
        newStat.extraInfo.label = '日均在线量';
      } else {
        newStat.extraInfo.label = '近一月日均在线量';
      }
    }
    return newStat;
  });
});

// 树样式计算属性
const treeStyle = computed((): CSSProperties => {
  return {
    height: "calc(100vh - 200px)",
    overflow: "auto"
  };
});

// 组织树过滤方法
const filterOrgNode = (value: string, data: OrgTreeNode) => {
  if (!value) return true;
  return data.name.indexOf(value) !== -1;
};

// 监听搜索关键词变化
watch(() => orgSearchKeyword.value, (val) => {
  orgTreeRef.value?.filter(val);
});

// 监听时间范围变化，重新加载图表数据
watch(() => chartFilters.period, async (newPeriod) => {
  if (newPeriod) {
    await loadTrendCharts(newPeriod as '24h' | '7d' | '30d');
    await nextTick();
    initChart();
  }
});

// 监听设备详情弹窗时间范围变化
watch(() => selectedTimeRange.value, () => {
  if (deviceDetailVisible.value && detailTrendChartInstance) {
    initDetailTrendChart();
  }
});

// 表格相关方法
const resetTablePageAndQuery = (keepFiltersFlag?: boolean) => {
  if (keepFiltersFlag !== true) {
    // 清除表格所有过滤器
    filters.value = [];
    tableRef.value?.clearAllFilters();
  }
  tablePage.current = 1;
  tablePage.currentPage = 1;
  loadDeviceList();
};

const selectionChangeHandler = (selRows: any[]) => {
  // 处理选择变化
  console.log('选中的行:', selRows);
};

// 表格过滤数据提供者
const filterDataProvider: TableFilterDataProvider = {
  options: (_prop: string, _filters: HeaderFilterValue[]) => {
    return new Promise<{ total: number; options: FilterOption[] }>(resolve => {
      // 这里应该调用实际的API来获取过滤选项
      // 暂时返回空数据，避免错误
      resolve({ total: 0, options: [] });
    });
  },
  onFilter: (filterValues: HeaderFilterValue[]) => {
    filters.value = filterValues;
    resetTablePageAndQuery(true); // 保持过滤器
  }
};

// 方法定义
const switchTab = async (tabKey: DeviceTabType) => {
  state.activeTab = tabKey;
  await loadDeviceList();
  await nextTick();
  initChart();
};

const handleOrgNodeClick = (_data: OrgTreeNode) => {
  loadDeviceList();
};

const getStatusTagType = (status: DeviceStatus): 'success' | 'info' | 'danger' | 'warning' => {
  const typeMap = {
    [DeviceStatus.ONLINE]: 'success' as const,
    [DeviceStatus.OFFLINE]: 'info' as const,
    [DeviceStatus.FAULT]: 'danger' as const,
    [DeviceStatus.MAINTENANCE]: 'warning' as const
  };
  return typeMap[status] || 'info';
};

const getStatusText = (status: DeviceStatus) => ({
  [DeviceStatus.ONLINE]: '在线',
  [DeviceStatus.OFFLINE]: '离线',
  [DeviceStatus.FAULT]: '故障',
  [DeviceStatus.MAINTENANCE]: '维护中'
}[status] || '未知');

// 迷你折线图SVG路径生成方法
const generateLinePoints = (data: number[]): string => {
  if (!data || data.length === 0) return '';

  const max = Math.max(...data);
  const min = Math.min(...data);
  const range = max - min || 1; // 避免除零

  return data.map((value, index) => {
    const x = (index / (data.length - 1)) * 100;
    const y = 20 - ((value - min) / range) * 16; // 留出上下边距
    return `${x},${y}`;
  }).join(' ');
};

const generateAreaPath = (data: number[]): string => {
  if (!data || data.length === 0) return '';

  const max = Math.max(...data);
  const min = Math.min(...data);
  const range = max - min || 1;

  let path = 'M 0,20'; // 从左下角开始

  // 绘制到第一个数据点
  const firstY = 20 - ((data[0] - min) / range) * 16;
  path += ` L 0,${firstY}`;

  // 绘制折线部分
  data.forEach((value, index) => {
    const x = (index / (data.length - 1)) * 100;
    const y = 20 - ((value - min) / range) * 16;
    path += ` L ${x},${y}`;
  });

  // 回到右下角并闭合路径
  path += ' L 100,20 Z';

  return path;
};

const generateDataPoints = (data: number[]): Array<{x: number, y: number}> => {
  if (!data || data.length === 0) return [];

  const max = Math.max(...data);
  const min = Math.min(...data);
  const range = max - min || 1;

  return data.map((value, index) => ({
    x: (index / (data.length - 1)) * 100,
    y: 20 - ((value - min) / range) * 16
  }));
};

// 设备详情弹窗相关方法
const showDeviceDetail = async (_device: any) => {
  deviceDetailVisible.value = true;
  await nextTick();
  initDetailCharts();
  loadDeviceListData();

  // 延迟调整图表大小，确保容器已完全渲染
  setTimeout(() => {
    if (detailTrendChartInstance) {
      detailTrendChartInstance.resize();
    }
  }, 100);
};

const handleCloseDetail = () => {
  deviceDetailVisible.value = false;

  // 清理图表实例
  if (detailTrendChartInstance) {
    detailTrendChartInstance.dispose();
    detailTrendChartInstance = null;
  }

  // 移除事件监听器
  if (detailTrendChartResizeHandler) {
    window.removeEventListener('resize', detailTrendChartResizeHandler);
    detailTrendChartResizeHandler = null;
  }
};

const searchDevices = () => {
  loadDeviceListData();
};

const handleDeviceSelection = (selection: any[]) => {
  console.log('选中的设备:', selection);
};

const handleDevicePageSizeChange = (size: number) => {
  deviceListPage.pageSize = size;
  loadDeviceListData();
};

const handleDevicePageChange = (page: number) => {
  deviceListPage.current = page;
  loadDeviceListData();
};

// 初始化详情弹窗图表
const initDetailCharts = () => {
  initDetailTrendChart();
};

// 初始化趋势图表
const initDetailTrendChart = () => {
  if (!detailTrendChartRef.value) return;

  if (detailTrendChartInstance) {
    detailTrendChartInstance.dispose();
  }

  detailTrendChartInstance = echarts.init(detailTrendChartRef.value);

  // 获取当前时间范围的数据
  const chartData = getDeviceDetailData(selectedTimeRange.value as '24h' | '7d' | '30d');

  // 计算Y轴最大值
  const maxOnline = Math.max(...chartData.map(item => item.onlineCount));
  const maxOffline = Math.max(...chartData.map(item => item.offlineCount));
  const yAxisMax = Math.max(maxOnline, maxOffline) + 2;

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['在线数量', '离线数量'],
      top: 8,
      right: 15,
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '3%',
      bottom: '5%',
      top: '12%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.map(item => item.date),
      axisLabel: {
        rotate: 45,
        fontSize: 11,
        margin: 5
      }
    },
    yAxis: {
      type: 'value',
      name: '数量',
      nameLocation: 'middle',
      nameGap: 25,
      nameRotate: 90,
      nameTextStyle: {
        fontSize: 12
      },
      max: yAxisMax,
      min: 0,
      axisLabel: {
        fontSize: 11
      }
    },
    series: [
      {
        name: '在线数量',
        type: 'line',
        data: chartData.map(item => item.onlineCount),
        // smooth: true,
        itemStyle: {
          color: '#67c23a'
        },
        lineStyle: {
          width: 2
        },
        symbol: 'circle',
        symbolSize: 4
      },
      {
        name: '离线数量',
        type: 'line',
        data: chartData.map(item => item.offlineCount),
        // smooth: true,
        itemStyle: {
          color: '#409eff'
        },
        lineStyle: {
          width: 2
        },
        symbol: 'circle',
        symbolSize: 4
      }
    ]
  };

  detailTrendChartInstance.setOption(option);

  // 添加窗口大小变化监听器
  if (detailTrendChartResizeHandler) {
    window.removeEventListener('resize', detailTrendChartResizeHandler);
  }
  detailTrendChartResizeHandler = () => {
    detailTrendChartInstance?.resize();
  };
  window.addEventListener('resize', detailTrendChartResizeHandler);
};



// 加载设备列表数据
const loadDeviceListData = () => {
  // 模拟设备列表数据
  const mockData = [
    {
      id: '1',
      name: '小七',
      status: '离线',
      collectTime: '2024-01-15 14:30:25',
      ipAddress: '************',
      macAddress: '38:D5:7A:E8:62:1D',
      lastOnlineTime: '2024-01-15 02:30:25'
    },
    {
      id: '2',
      name: '护士站',
      status: '在线',
      collectTime: '2024-01-15 14:30:25',
      ipAddress: '************',
      macAddress: '38:D5:7A:E8:62:1E',
      lastOnlineTime: '2024-01-15 13:30:25'
    },
    {
      id: '3',
      name: '工作站1',
      status: '在线',
      collectTime: '2024-01-15 14:30:25',
      ipAddress: '************',
      macAddress: '38:D5:7A:E8:62:1F',
      lastOnlineTime: '2024-01-15 14:00:25'
    },
    {
      id: '4',
      name: '工作站2',
      status: '离线',
      collectTime: '2024-01-15 14:30:25',
      ipAddress: '************',
      macAddress: '38:D5:7A:E8:62:20',
      lastOnlineTime: '2024-01-15 12:30:25'
    },
    {
      id: '5',
      name: '工作站3',
      status: '在线',
      collectTime: '2024-01-15 14:30:25',
      ipAddress: '************',
      macAddress: '38:D5:7A:E8:62:21',
      lastOnlineTime: '2024-01-15 14:15:25'
    }
  ];

  // 模拟搜索过滤
  let filteredData = mockData;
  if (searchKeyword.value) {
    filteredData = mockData.filter(item =>
      item.name.includes(searchKeyword.value) ||
      item.ipAddress.includes(searchKeyword.value) ||
      item.macAddress.includes(searchKeyword.value) ||
      item.collectTime.includes(searchKeyword.value) ||
      item.lastOnlineTime.includes(searchKeyword.value)
    );
  }

  // 模拟分页
  const start = (deviceListPage.current - 1) * deviceListPage.pageSize;
  const end = start + deviceListPage.pageSize;
  deviceListData.value = filteredData.slice(start, end);
  deviceListPage.total = filteredData.length;
};

// 任务配置弹窗相关方法
const showTaskConfig = () => {
  taskConfigVisible.value = true;
  // 初始化调度组件
  nextTick(() => {
    if (simpleScheduleRef.value) {
      simpleScheduleRef.value.initExp(taskConfig.schedulingCycle);
    }
  });
};

const handleCloseTaskConfig = () => {
  taskConfigVisible.value = false;
};

const handleSaveTaskConfig = () => {
  // 生成调度表达式
  if (taskConfig.enableSchedule && simpleScheduleRef.value) {
    simpleScheduleRef.value.generateExp();
  }

  // 这里可以添加保存任务配置的逻辑
  console.log('保存任务配置:', taskConfig);
  ElMessage.success('任务配置保存成功');
  taskConfigVisible.value = false;
};

const handleSearch = () => {
  tablePage.current = 1;
  tablePage.currentPage = 1;
  state.pagination.current = 1; // 保持兼容性
  loadDeviceList();
};

const handleReset = () => {
  columnCondition.value = null;
  columnCondition.field = "ipAddress";
  columnCondition.fuzzyable = true;
  tablePage.current = 1;
  tablePage.currentPage = 1;
  state.tableFilters.searchKeyword = '';
  state.pagination.current = 1;
  loadDeviceList();
};

const handleOperation = async (operation: string, device: any) => {
  try {
    if (operation === '查看') {
      await showDeviceDetail(device);
      return;
    }

    if (operation === '任务配置') {
      showTaskConfig();
      return;
    }

    const actions = {
      '在线状态': () => apiService.operation.toggleOnlineStatus(device.id),
      '移除': () => apiService.operation.removeDevice(device.id)
    };

    await actions[operation]?.();
    ElMessage.success(`${operation}成功`);
    loadDeviceList();
  } catch (error) {
    ElMessage.error('操作失败');
  }
};



// 数据加载方法
const loadStatistics = async () => {
  try {
    const [serverStats, terminalStats] = await Promise.all([
      apiService.server.getStatistics(),
      apiService.terminal.getStatistics()
    ]);

    if (serverStats.success) {
      serverStatistics.value = serverStats.data;
    }

    if (terminalStats.success) {
      terminalStatistics.value = terminalStats.data;
    }
  } catch (error) {
    ElMessage.error('加载统计数据失败');
  }
};

const loadTrendCharts = async (timeRange: '24h' | '7d' | '30d' = '24h') => {
  try {
    const [serverChart, terminalChart] = await Promise.all([
      apiService.server.getTrendChart(timeRange),
      apiService.terminal.getTrendChart(timeRange)
    ]);

    if (serverChart.success && serverChart.data) {
      serverTrendChart.value.data = serverChart.data.data || [];
      serverTrendChart.value.filters = serverChart.data.filters;
      serverTrendChart.value.title = serverChart.data.title;
    }

    if (terminalChart.success && terminalChart.data) {
      terminalTrendChart.value.data = terminalChart.data.data || [];
      terminalTrendChart.value.filters = terminalChart.data.filters;
      terminalTrendChart.value.title = terminalChart.data.title;
    }
  } catch (error) {
    ElMessage.error('加载图表数据失败');
  }
};

const loadDeviceList = async () => {
  tableLoading.value = true;
  try {
    const api = state.activeTab === DeviceTabType.SERVER ? apiService.server : apiService.terminal;

    // 构建查询参数 - 修正参数格式
    const queryParams = {
      searchKeyword: columnCondition.value || '',
      searchField: columnCondition.field,
      fuzzy: columnCondition.fuzzyable,
      pageNum: tablePage.current,
      pageSize: tablePage.pageSize,
      // 添加过滤器参数
      headerFilter: {
        filters: filters.value
      }
    };

    const response = await api.getDeviceList(queryParams, tablePage);

    if (response.success) {
      tableData.value = response.data.list || [];
      tablePage.total = response.data.pagination?.total || 0;

      // 同时更新原有的状态以保持兼容性
      if (state.activeTab === DeviceTabType.SERVER) {
        state.serverDevices = response.data.list as any[];
      } else {
        state.terminalDevices = response.data.list as any[];
      }
      state.pagination = response.data.pagination || {
        current: tablePage.current,
        pageSize: tablePage.pageSize,
        total: tablePage.total
      };
    }
  } catch (error) {
    console.error('加载设备列表失败:', error);
    ElMessage.error('加载设备列表失败');
  } finally {
    tableLoading.value = false;
    state.loading = false;
  }
};

// 图表初始化
const initChart = () => {
  if (!trendChartRef.value || !currentTrendChart.value.data) return;

  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(trendChartRef.value);

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['在线数量', '总数量'],
      top: 5,
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '1%',
      right: '1%',
      bottom: '1%',
      top: '18%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: currentTrendChart.value.data.map(item => item.date),
      axisLabel: {
        fontSize: 11
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 11
      }
    },
    series: [
      {
        name: '在线数量',
        type: 'line',
        data: currentTrendChart.value.data.map(item => item.onlineCount),
        itemStyle: { color: '#52c41a' }
      },
      {
        name: '总数量',
        type: 'line',
        data: currentTrendChart.value.data.map(item => item.totalCount),
        itemStyle: { color: '#1890ff' }
      }
    ]
  };

  chartInstance.setOption(option);

  // 添加窗口大小变化监听器
  if (chartResizeHandler) {
    window.removeEventListener('resize', chartResizeHandler);
  }
  chartResizeHandler = () => {
    chartInstance?.resize();
  };
  window.addEventListener('resize', chartResizeHandler);
};



// 生命周期
onMounted(async () => {
  await Promise.all([
    loadStatistics(),
    loadTrendCharts(chartFilters.period as '24h' | '7d' | '30d'),
    loadDeviceList()
  ]);

  await nextTick();
  initChart();
});

// 组件卸载前清理
onBeforeUnmount(() => {
  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  if (detailTrendChartInstance) {
    detailTrendChartInstance.dispose();
    detailTrendChartInstance = null;
  }

  // 移除窗口事件监听器
  if (chartResizeHandler) {
    window.removeEventListener('resize', chartResizeHandler);
    chartResizeHandler = null;
  }

  if (detailTrendChartResizeHandler) {
    window.removeEventListener('resize', detailTrendChartResizeHandler);
    detailTrendChartResizeHandler = null;
  }
});
</script>

<style scoped lang="scss">
.network-device-monitor {
  height: 100vh;

  .left-panel {
    padding: 16px;
    height: 100%;
    overflow: hidden;

    .org-header {
      margin-bottom: 16px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .org-tree {
      .el-input {
        margin-bottom: 12px;
      }

      .el-tree {
        background: transparent;
      }
    }
  }

  .main-content {
    padding: 16px;
    height: 100%;
    overflow-y: auto;

    .tab-container {
      display: flex;
      margin-bottom: 16px;
      border-bottom: 1px solid #e4e7ed;

      .tab-item {
        padding: 12px 24px;
        cursor: pointer;
        border-bottom: 2px solid transparent;
        color: #606266;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          color: #409eff;
        }

        &.active {
          color: #409eff;
          border-bottom-color: #409eff;
        }
      }
    }

    .statistics-section {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;
      margin-bottom: 16px;

      .stat-card {
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease;

        &:hover {
          transform: translateY(-2px);
        }

        .stat-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .stat-title {
            font-size: 14px;
            font-weight: 500;
          }

          .el-icon-question {
            cursor: pointer;
          }
        }

        .stat-value {
          font-size: 32px;
          font-weight: 700;
          margin-bottom: 8px;
        }

        .stat-footer {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .stat-trend {
            font-size: 12px;
            display: flex;
            align-items: center;

            .trend-label {
              opacity: 0.7;
              margin-right: 4px;
            }

            .trend-value {
              font-weight: 600;

              &.up {
                color: #67c23a;
              }

              &.down {
                color: #f56c6c;
              }
            }
          }

          .stat-extra {
            font-size: 12px;
            display: flex;
            align-items: center;

            .extra-label {
              opacity: 0.7;
              margin-right: 4px;
            }

            .extra-value {
              font-weight: 600;
              color: #409eff;
            }
          }

          .stat-chart {
            margin-top: 8px;

            .mini-line-chart {
              width: 100%;
              height: 20px;
              border-radius: 2px;
              overflow: hidden;
              transition: all 0.3s ease;

              &:hover {
                transform: scaleY(1.1);
              }

              svg {
                width: 100%;
                height: 100%;
                display: block;

                polyline {
                  transition: stroke-width 0.3s ease;
                }

                path {
                  transition: opacity 0.3s ease;
                }
              }

              &:hover svg {
                polyline {
                  stroke-width: 2;
                }

                path {
                  opacity: 0.8;
                }

                .data-points {
                  opacity: 1 !important;
                }
              }
            }

            .mini-bar-chart {
              display: flex;
              align-items: end;
              gap: 2px;
              height: 20px;
              width: 100%;

              .bar-item {
                flex: 1;
                min-height: 2px;
                border-radius: 1px;
                opacity: 0.8;
              }
            }

            .mini-progress-chart {
              width: 100%;

              .progress-bg {
                width: 100%;
                height: 6px;
                background-color: #f0f0f0;
                border-radius: 3px;
                overflow: hidden;

                .progress-fill {
                  height: 100%;
                  border-radius: 3px;
                  transition: width 0.3s ease;
                }
              }
            }
          }
        }
      }
    }

    .chart-section {
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }

        .chart-filters {
          display: flex;
          gap: 12px;

          .el-select {
            width: 120px;
          }
        }
      }

      .chart-content {
        .trend-chart {
          width: 100%;
          height: 300px;
          overflow: hidden;
          position: relative;
        }
      }
    }

    .device-list-section {
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .list-header {
        margin-bottom: 16px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
        }
      }

      .status-tag-container {
        display: flex;
        justify-content: center;
        align-items: center;

        .status-tag {
          font-weight: 500;
        }
      }

      .pagination-wrapper {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .network-device-monitor {
    .main-content {
      .statistics-section {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }
}

@media (max-width: 768px) {
  .network-device-monitor {
    flex-direction: column;

    .left-sidebar {
      width: 100%;
      height: auto;
    }

    .main-content {
      .statistics-section {
        grid-template-columns: 1fr;
      }

      .chart-section .chart-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;

        .chart-filters {
          justify-content: center;
        }
      }


    }
  }
}

// 设备详情弹窗样式
:deep(.device-detail-dialog) {
  .el-dialog {
    max-height: 90vh;
  }

  .el-dialog__body {
    padding: 20px;
    max-height: 75vh;
    overflow-y: auto;
  }

  .device-detail-content {
    .chart-section {
      margin-bottom: 16px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 12px;

      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .chart-controls {
          display: flex;
          gap: 8px;
        }

        .time-selector {
          display: flex;
          align-items: center;
        }
      }

      .trend-chart {
        height: 300px;
        width: 100%;
        overflow: hidden;
        position: relative;
      }
    }

    .search-section {
      margin-bottom: 12px;
      display: flex;
      justify-content: flex-start;
    }

    .device-list-section {
      .pagination-container {
        margin-top: 12px;
        display: flex;
        justify-content: center;
      }
    }
  }
}

// 响应式适配
@media (max-width: 1200px) {
  :deep(.device-detail-dialog) {
    .el-dialog {
      width: 85% !important;
      margin: 5vh auto !important;
    }

    .device-detail-content {
      .chart-section {
        .trend-chart {
          height: 240px;
        }
      }
    }
  }
}

// 任务配置弹窗样式
:deep(.task-config-dialog) {
  .el-dialog {
    max-height: 90vh;
  }

  .el-dialog__body {
    padding: 20px;
    max-height: 75vh;
    overflow-y: auto;
  }

  .task-config-content {
    .config-row {
      display: flex;
      align-items: flex-start;
      margin-bottom: 20px;
      min-height: 32px;

      .config-label {
        width: 100px;
        text-align: right;
        padding-right: 12px;
        font-weight: 500;
        color: #606266;
        line-height: 32px;
        flex-shrink: 0;
      }

      .config-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 12px;

        .el-checkbox {
          margin-right: 20px;
        }

        .schedule-config {
          margin-top: 8px;
          padding: 12px;
          background-color: #f5f7fa;
          border-radius: 4px;
          border: 1px solid #e4e7ed;
        }

        .one-time-config {
          margin-top: 8px;
        }
      }
    }
  }

  .dialog-footer {
    text-align: right;
    padding-top: 10px;
    border-top: 1px solid #e4e7ed;

    .el-button {
      margin-left: 10px;
    }
  }
}


</style>
